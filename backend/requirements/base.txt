# Code Med Talent - Base Requirements
# Core Django and database requirements (Python 3.9 compatible)

# Django Framework
Django==4.2.7
djangorestframework==3.14.0
django-cors-headers==4.3.1
django-filter==23.3
django-extensions==3.2.3

# Database
psycopg2-binary==2.9.7
redis==4.6.0
django-redis==5.4.0

# Authentication & Security
djangorestframework-simplejwt==5.3.0
django-oauth-toolkit==1.7.1
cryptography==41.0.7
bcrypt==4.0.1

# API Documentation
drf-spectacular==0.26.5

# File Handling & Storage
Pillow==10.0.1
django-storages==1.14.2
boto3==1.29.7
botocore==1.32.7

# Background Tasks
celery==5.3.4
django-celery-beat==2.5.0
django-celery-results==2.5.1

# AI & Machine Learning (Python 3.9 compatible versions)
openai==0.28.1
langchain==0.0.340
transformers==4.35.2
torch==2.1.1
torchvision==0.16.1
scikit-learn==1.3.2
pandas==2.1.3
numpy==1.25.2

# OCR & Document Processing
pytesseract==0.3.10
pdf2image==1.16.3
opencv-python==4.8.1.78
pypdf2==3.0.1
google-cloud-vision==3.4.5
google-auth==2.23.4

# Blockchain Integration (Hedera Hashgraph)
hedera-sdk-py==2.17.0

# Text Processing and NLP
nltk==3.8.1
textdistance==4.6.0

# Communication
twilio==8.10.0
sendgrid==6.10.0

# HTTP Requests
requests==2.31.0
httpx==0.25.2

# Data Validation
pydantic==1.10.13
marshmallow==3.20.1

# Utilities
python-decouple==3.8
python-dotenv==1.0.0
pytz==2023.3
python-dateutil==2.8.2

# Monitoring & Logging
sentry-sdk==1.38.0
structlog==23.2.0

# Testing (included in base for CI/CD)
pytest==7.4.3
pytest-django==4.7.0
factory-boy==3.3.0

# Development utilities
ipython==8.17.2
django-debug-toolbar==4.2.0
