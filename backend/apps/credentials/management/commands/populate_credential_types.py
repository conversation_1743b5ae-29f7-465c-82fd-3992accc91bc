"""
Management command to populate healthcare credential types.
"""

from django.core.management.base import BaseCommand
from django.db import transaction

from apps.credentials.models import CredentialType


class Command(BaseCommand):
    help = 'Populate healthcare credential types for CredentialChain system'

    def handle(self, *args, **options):
        """Populate credential types."""
        
        credential_types_data = [
            # Professional Licenses
            {
                'name': 'Registered Nurse (RN) License',
                'category': CredentialType.Category.LICENSE,
                'description': 'State-issued license to practice as a Registered Nurse',
                'issuing_authority': 'State Board of Nursing',
                'typical_validity_period': 24,  # months
                'required_for_roles': ['Registered Nurse', 'Charge Nurse', 'Nurse Manager'],
                'verification_requirements': {
                    'primary_source': 'State Nursing Board Database',
                    'renewal_required': True,
                    'continuing_education': True
                }
            },
            {
                'name': 'Licensed Practical Nurse (LPN) License',
                'category': CredentialType.Category.LICENSE,
                'description': 'State-issued license to practice as a Licensed Practical Nurse',
                'issuing_authority': 'State Board of Nursing',
                'typical_validity_period': 24,
                'required_for_roles': ['Licensed Practical Nurse', 'LPN'],
                'verification_requirements': {
                    'primary_source': 'State Nursing Board Database',
                    'renewal_required': True
                }
            },
            {
                'name': 'Certified Nursing Assistant (CNA) License',
                'category': CredentialType.Category.LICENSE,
                'description': 'State certification to work as a Nursing Assistant',
                'issuing_authority': 'State Department of Health',
                'typical_validity_period': 24,
                'required_for_roles': ['Certified Nursing Assistant', 'CNA'],
                'verification_requirements': {
                    'primary_source': 'State CNA Registry',
                    'renewal_required': True
                }
            },
            
            # Life Support Certifications
            {
                'name': 'Basic Life Support (BLS) Certification',
                'category': CredentialType.Category.CERTIFICATION,
                'description': 'CPR and basic life support certification',
                'issuing_authority': 'American Heart Association',
                'typical_validity_period': 24,
                'required_for_roles': ['All Healthcare Providers'],
                'verification_requirements': {
                    'primary_source': 'AHA Verification System',
                    'renewal_required': True
                }
            },
            {
                'name': 'Advanced Cardiac Life Support (ACLS) Certification',
                'category': CredentialType.Category.CERTIFICATION,
                'description': 'Advanced cardiac life support certification',
                'issuing_authority': 'American Heart Association',
                'typical_validity_period': 24,
                'required_for_roles': ['ICU Nurse', 'Emergency Nurse', 'Critical Care'],
                'verification_requirements': {
                    'primary_source': 'AHA Verification System',
                    'renewal_required': True
                }
            },
            {
                'name': 'Pediatric Advanced Life Support (PALS) Certification',
                'category': CredentialType.Category.CERTIFICATION,
                'description': 'Pediatric advanced life support certification',
                'issuing_authority': 'American Heart Association',
                'typical_validity_period': 24,
                'required_for_roles': ['Pediatric Nurse', 'NICU Nurse', 'Emergency Nurse'],
                'verification_requirements': {
                    'primary_source': 'AHA Verification System',
                    'renewal_required': True
                }
            },
            
            # Specialty Certifications
            {
                'name': 'Critical Care Registered Nurse (CCRN) Certification',
                'category': CredentialType.Category.CERTIFICATION,
                'description': 'Specialty certification for critical care nursing',
                'issuing_authority': 'American Association of Critical-Care Nurses',
                'typical_validity_period': 36,
                'required_for_roles': ['ICU Nurse', 'Critical Care Nurse'],
                'verification_requirements': {
                    'primary_source': 'AACN Certification Corporation',
                    'renewal_required': True,
                    'continuing_education': True
                }
            },
            {
                'name': 'Certified Emergency Nurse (CEN) Certification',
                'category': CredentialType.Category.CERTIFICATION,
                'description': 'Specialty certification for emergency nursing',
                'issuing_authority': 'Board of Certification for Emergency Nursing',
                'typical_validity_period': 48,
                'required_for_roles': ['Emergency Nurse', 'Trauma Nurse'],
                'verification_requirements': {
                    'primary_source': 'BCEN Verification System',
                    'renewal_required': True
                }
            },
            {
                'name': 'Certified Medical-Surgical Registered Nurse (CMSRN)',
                'category': CredentialType.Category.CERTIFICATION,
                'description': 'Specialty certification for medical-surgical nursing',
                'issuing_authority': 'Medical-Surgical Nursing Certification Board',
                'typical_validity_period': 60,
                'required_for_roles': ['Med-Surg Nurse', 'Floor Nurse'],
                'verification_requirements': {
                    'primary_source': 'MSNCB Verification System',
                    'renewal_required': True
                }
            },
            
            # Education Credentials
            {
                'name': 'Bachelor of Science in Nursing (BSN)',
                'category': CredentialType.Category.EDUCATION,
                'description': 'Bachelor\'s degree in nursing',
                'issuing_authority': 'Accredited Nursing School',
                'typical_validity_period': None,  # Permanent
                'required_for_roles': ['Nurse Manager', 'Clinical Specialist'],
                'verification_requirements': {
                    'primary_source': 'National Student Clearinghouse',
                    'renewal_required': False
                }
            },
            {
                'name': 'Associate Degree in Nursing (ADN)',
                'category': CredentialType.Category.EDUCATION,
                'description': 'Associate degree in nursing',
                'issuing_authority': 'Accredited Nursing School',
                'typical_validity_period': None,
                'required_for_roles': ['Registered Nurse'],
                'verification_requirements': {
                    'primary_source': 'National Student Clearinghouse',
                    'renewal_required': False
                }
            },
            
            # Background Checks
            {
                'name': 'FBI Background Check',
                'category': CredentialType.Category.BACKGROUND,
                'description': 'Federal criminal background check',
                'issuing_authority': 'Federal Bureau of Investigation',
                'typical_validity_period': 12,
                'required_for_roles': ['All Healthcare Providers'],
                'verification_requirements': {
                    'primary_source': 'FBI CJIS Division',
                    'renewal_required': True
                }
            },
            {
                'name': 'State Background Check',
                'category': CredentialType.Category.BACKGROUND,
                'description': 'State criminal background check',
                'issuing_authority': 'State Bureau of Investigation',
                'typical_validity_period': 12,
                'required_for_roles': ['All Healthcare Providers'],
                'verification_requirements': {
                    'primary_source': 'State Criminal Database',
                    'renewal_required': True
                }
            },
            
            # Additional Training
            {
                'name': 'HIPAA Training Certification',
                'category': CredentialType.Category.TRAINING,
                'description': 'Health Insurance Portability and Accountability Act training',
                'issuing_authority': 'Healthcare Facility or Training Provider',
                'typical_validity_period': 12,
                'required_for_roles': ['All Healthcare Providers'],
                'verification_requirements': {
                    'primary_source': 'Training Provider Records',
                    'renewal_required': True
                }
            },
            {
                'name': 'OSHA Training Certification',
                'category': CredentialType.Category.TRAINING,
                'description': 'Occupational Safety and Health Administration training',
                'issuing_authority': 'OSHA or Authorized Training Provider',
                'typical_validity_period': 36,
                'required_for_roles': ['All Healthcare Providers'],
                'verification_requirements': {
                    'primary_source': 'OSHA Training Records',
                    'renewal_required': True
                }
            },
            {
                'name': 'Infection Control Training',
                'category': CredentialType.Category.TRAINING,
                'description': 'Infection prevention and control training',
                'issuing_authority': 'Healthcare Facility or CDC',
                'typical_validity_period': 12,
                'required_for_roles': ['All Healthcare Providers'],
                'verification_requirements': {
                    'primary_source': 'Training Provider Records',
                    'renewal_required': True
                }
            },
        ]
        
        with transaction.atomic():
            created_count = 0
            updated_count = 0
            
            for cred_data in credential_types_data:
                credential_type, created = CredentialType.objects.get_or_create(
                    name=cred_data['name'],
                    defaults=cred_data
                )
                
                if created:
                    created_count += 1
                    self.stdout.write(
                        self.style.SUCCESS(f'Created: {credential_type.name}')
                    )
                else:
                    # Update existing record
                    for key, value in cred_data.items():
                        if key != 'name':  # Don't update the name (unique field)
                            setattr(credential_type, key, value)
                    credential_type.save()
                    updated_count += 1
                    self.stdout.write(
                        self.style.WARNING(f'Updated: {credential_type.name}')
                    )
        
        self.stdout.write(
            self.style.SUCCESS(
                f'\nCompleted! Created {created_count} new credential types, '
                f'updated {updated_count} existing ones.'
            )
        )
