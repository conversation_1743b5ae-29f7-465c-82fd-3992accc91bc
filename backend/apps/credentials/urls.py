"""
Code Med Talent - Credentials URLs
URL patterns for CredentialChain API endpoints.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter

from .views import (
    CredentialTypeViewSet,
    CredentialViewSet,
    CredentialUploadView,
    CredentialVerificationView,
    credential_stats,
    reprocess_credential
)

# Create router for ViewSets
router = DefaultRouter()
router.register(r'types', CredentialTypeViewSet, basename='credential-types')
router.register(r'credentials', CredentialViewSet, basename='credentials')

app_name = 'credentials'

urlpatterns = [
    # Include router URLs
    path('', include(router.urls)),
    
    # Custom API endpoints
    path('upload/', CredentialUploadView.as_view(), name='credential-upload'),
    path('verify/<uuid:credential_id>/', CredentialVerificationView.as_view(), name='credential-verify'),
    path('stats/', credential_stats, name='credential-stats'),
    path('reprocess/<uuid:credential_id>/', reprocess_credential, name='credential-reprocess'),
]
