# Generated by Django 4.2.7 on 2025-06-16 20:44

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='CredentialType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Name of the credential type (e.g., RN License, BLS Certification)', max_length=100, unique=True)),
                ('category', models.CharField(choices=[('license', 'Professional License'), ('certification', 'Certification'), ('education', 'Education/Degree'), ('training', 'Training/Course'), ('background', 'Background Check')], help_text='Category of credential', max_length=20)),
                ('description', models.TextField(blank=True, help_text='Description of what this credential represents')),
                ('requires_verification', models.BooleanField(default=True, help_text='Whether this credential requires third-party verification')),
                ('verification_source', models.CharField(blank=True, help_text='Primary source for verification (e.g., State Nursing Board)', max_length=200)),
                ('verification_url', models.URLField(blank=True, help_text='URL for verification lookup')),
                ('has_expiration', models.BooleanField(default=True, help_text='Whether this credential expires')),
                ('default_validity_months', models.PositiveIntegerField(blank=True, help_text='Default validity period in months', null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Credential Type',
                'verbose_name_plural': 'Credential Types',
                'db_table': 'credentials_type',
                'ordering': ['category', 'name'],
            },
        ),
        migrations.CreateModel(
            name='Credential',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('credential_number', models.CharField(help_text='Official credential/license number', max_length=100)),
                ('issuing_authority', models.CharField(help_text='Organization that issued the credential', max_length=200)),
                ('issuing_state', models.CharField(blank=True, help_text='State where credential was issued (if applicable)', max_length=50)),
                ('issue_date', models.DateField(help_text='Date the credential was issued')),
                ('expiration_date', models.DateField(blank=True, help_text='Date the credential expires', null=True)),
                ('status', models.CharField(choices=[('pending', 'Pending Verification'), ('verified', 'Verified'), ('expired', 'Expired'), ('invalid', 'Invalid/Rejected'), ('revoked', 'Revoked')], default='pending', max_length=20)),
                ('verification_date', models.DateTimeField(blank=True, help_text='When the credential was last verified', null=True)),
                ('verification_source', models.CharField(blank=True, help_text='Source used for verification', max_length=200)),
                ('verification_notes', models.TextField(blank=True, help_text='Notes from verification process')),
                ('blockchain_hash', models.CharField(blank=True, help_text='Blockchain hash for Career Passport', max_length=128)),
                ('document_file', models.FileField(help_text='Scanned copy of the credential', upload_to='credentials/', validators=[django.core.validators.FileExtensionValidator(['pdf', 'jpg', 'jpeg', 'png'])])),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('credential_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='credentials', to='credentials.credentialtype')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='credentials', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Credential',
                'verbose_name_plural': 'Credentials',
                'db_table': 'credentials_credential',
                'indexes': [models.Index(fields=['user', 'status'], name='credentials_user_id_f7fa95_idx'), models.Index(fields=['credential_number'], name='credentials_credent_b122ed_idx'), models.Index(fields=['expiration_date'], name='credentials_expirat_02e768_idx'), models.Index(fields=['status'], name='credentials_status_6107c5_idx')],
                'unique_together': {('user', 'credential_type', 'credential_number')},
            },
        ),
    ]
