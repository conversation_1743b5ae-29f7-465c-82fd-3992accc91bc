"""
Code Med Talent - Credentials API Views
API endpoints for CredentialChain system.
"""

import logging
from typing import Dict, Any

from django.shortcuts import get_object_or_404
from django.db import transaction
from django.utils import timezone
from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.parsers import <PERSON>PartParser, FormParser
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.viewsets import ModelViewSet
from drf_spectacular.utils import extend_schema, OpenApiParameter
from drf_spectacular.types import OpenApiTypes

from .models import CredentialType, Credential
from .serializers import (
    CredentialTypeSerializer,
    CredentialSerializer,
    CredentialUploadSerializer,
    CredentialVerificationSerializer
)
from .services import OCRService, CredentialExtractor

logger = logging.getLogger(__name__)


class CredentialTypeViewSet(ModelViewSet):
    """
    ViewSet for managing credential types.
    """
    queryset = CredentialType.objects.all()
    serializer_class = CredentialTypeSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()
        category = self.request.query_params.get('category')
        if category:
            queryset = queryset.filter(category=category)
        return queryset


class CredentialViewSet(ModelViewSet):
    """
    ViewSet for managing user credentials.
    """
    serializer_class = CredentialSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        # Users can only see their own credentials
        return Credential.objects.filter(user=self.request.user)

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)


class CredentialUploadView(APIView):
    """
    API endpoint for uploading and processing credential documents.
    """
    permission_classes = [permissions.IsAuthenticated]
    parser_classes = [MultiPartParser, FormParser]

    @extend_schema(
        request=CredentialUploadSerializer,
        responses={
            200: CredentialSerializer,
            400: OpenApiTypes.OBJECT,
        },
        description="Upload and process a credential document using OCR and AI extraction"
    )
    def post(self, request):
        """
        Upload and process a credential document.

        This endpoint:
        1. Accepts uploaded document (PDF, JPG, PNG)
        2. Processes it with OCR to extract text
        3. Uses AI to extract structured credential data
        4. Creates a new Credential record
        5. Returns the processed credential information
        """
        serializer = CredentialUploadSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        try:
            with transaction.atomic():
                # Get validated data
                document_file = serializer.validated_data['document_file']
                credential_type_id = serializer.validated_data.get('credential_type_id')
                use_google_vision = serializer.validated_data.get('use_google_vision', True)

                # Get credential type if provided
                credential_type = None
                if credential_type_id:
                    credential_type = get_object_or_404(CredentialType, id=credential_type_id)

                # Process document with OCR
                ocr_service = OCRService()
                ocr_result = ocr_service.process_document(document_file, use_google_vision)

                if ocr_result.error_message:
                    return Response(
                        {'error': f'OCR processing failed: {ocr_result.error_message}'},
                        status=status.HTTP_400_BAD_REQUEST
                    )

                # Extract credential data using AI
                extractor = CredentialExtractor()
                credential_type_hint = credential_type.name if credential_type else None
                extracted_data = extractor.extract_credential_data(ocr_result, credential_type_hint)

                # Determine credential type if not provided
                if not credential_type and extracted_data.credential_type:
                    try:
                        credential_type = CredentialType.objects.get(
                            name__icontains=extracted_data.credential_type
                        )
                    except CredentialType.DoesNotExist:
                        # Create new credential type if not found
                        credential_type = CredentialType.objects.create(
                            name=extracted_data.credential_type,
                            category=CredentialType.Category.CERTIFICATION,
                            description=f"Auto-created from document upload"
                        )

                # Create credential record
                credential_data = {
                    'user': request.user,
                    'credential_type': credential_type,
                    'document_file': document_file,
                    'license_number': extracted_data.license_number or '',
                    'holder_name': extracted_data.holder_name or '',
                    'issuing_authority': extracted_data.issuing_authority or '',
                    'status': Credential.Status.PENDING,
                    'ai_extracted_data': {
                        'ocr_text': ocr_result.text,
                        'ocr_confidence': ocr_result.confidence,
                        'ocr_method': ocr_result.processing_method,
                        'extraction_confidence': extracted_data.confidence_score,
                        'extracted_fields': extracted_data.extracted_fields or {}
                    }
                }

                # Parse dates if available
                if extracted_data.issue_date:
                    try:
                        from datetime import datetime
                        credential_data['issue_date'] = datetime.strptime(
                            extracted_data.issue_date, '%m/%d/%Y'
                        ).date()
                    except ValueError:
                        pass

                if extracted_data.expiration_date:
                    try:
                        from datetime import datetime
                        credential_data['expiration_date'] = datetime.strptime(
                            extracted_data.expiration_date, '%m/%d/%Y'
                        ).date()
                    except ValueError:
                        pass

                credential = Credential.objects.create(**credential_data)

                # Return serialized credential
                response_serializer = CredentialSerializer(credential)
                return Response(response_serializer.data, status=status.HTTP_201_CREATED)

        except Exception as e:
            logger.error(f"Credential upload processing failed: {e}")
            return Response(
                {'error': 'Failed to process credential document'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class CredentialVerificationView(APIView):
    """
    API endpoint for manual credential verification by admins.
    """
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        request=CredentialVerificationSerializer,
        responses={
            200: CredentialSerializer,
            400: OpenApiTypes.OBJECT,
            403: OpenApiTypes.OBJECT,
            404: OpenApiTypes.OBJECT,
        },
        description="Manually verify or reject a credential (admin only)"
    )
    def post(self, request, credential_id):
        """
        Manually verify or reject a credential.
        Only admins can perform verification.
        """
        # Check admin permissions
        if not request.user.is_staff:
            return Response(
                {'error': 'Only administrators can verify credentials'},
                status=status.HTTP_403_FORBIDDEN
            )

        credential = get_object_or_404(Credential, id=credential_id)
        serializer = CredentialVerificationSerializer(data=request.data)

        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Update credential status
            verification_data = serializer.validated_data
            credential.status = verification_data['status']
            credential.verification_notes = verification_data.get('verification_notes', '')
            credential.verification_source = verification_data.get('verification_source', 'Manual Review')
            credential.verification_date = timezone.now()
            credential.verified_by = request.user

            credential.save()

            # TODO: Trigger blockchain hash creation for verified credentials
            # TODO: Send notification to credential owner

            response_serializer = CredentialSerializer(credential)
            return Response(response_serializer.data)

        except Exception as e:
            logger.error(f"Credential verification failed: {e}")
            return Response(
                {'error': 'Failed to update credential verification'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def credential_stats(request):
    """
    Get credential statistics for the current user.
    """
    user_credentials = Credential.objects.filter(user=request.user)

    stats = {
        'total_credentials': user_credentials.count(),
        'verified_credentials': user_credentials.filter(status=Credential.Status.VERIFIED).count(),
        'pending_credentials': user_credentials.filter(status=Credential.Status.PENDING).count(),
        'expired_credentials': user_credentials.filter(status=Credential.Status.EXPIRED).count(),
        'by_category': {}
    }

    # Group by credential type category
    for category in CredentialType.Category.choices:
        category_count = user_credentials.filter(
            credential_type__category=category[0]
        ).count()
        if category_count > 0:
            stats['by_category'][category[1]] = category_count

    return Response(stats)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def reprocess_credential(request, credential_id):
    """
    Reprocess a credential document with updated OCR/AI.
    """
    credential = get_object_or_404(Credential, id=credential_id, user=request.user)

    if not credential.document_file:
        return Response(
            {'error': 'No document file available for reprocessing'},
            status=status.HTTP_400_BAD_REQUEST
        )

    try:
        # Reprocess with OCR
        ocr_service = OCRService()
        ocr_result = ocr_service.process_document(credential.document_file, use_google_vision=True)

        # Extract data with AI
        extractor = CredentialExtractor()
        extracted_data = extractor.extract_credential_data(
            ocr_result,
            credential.credential_type.name if credential.credential_type else None
        )

        # Update credential with new data
        credential.ai_extracted_data = {
            'ocr_text': ocr_result.text,
            'ocr_confidence': ocr_result.confidence,
            'ocr_method': ocr_result.processing_method,
            'extraction_confidence': extracted_data.confidence_score,
            'extracted_fields': extracted_data.extracted_fields or {},
            'reprocessed_at': timezone.now().isoformat()
        }

        # Update fields if extraction confidence is high
        if extracted_data.confidence_score > 0.7:
            if extracted_data.license_number:
                credential.license_number = extracted_data.license_number
            if extracted_data.holder_name:
                credential.holder_name = extracted_data.holder_name
            if extracted_data.issuing_authority:
                credential.issuing_authority = extracted_data.issuing_authority

        credential.save()

        serializer = CredentialSerializer(credential)
        return Response(serializer.data)

    except Exception as e:
        logger.error(f"Credential reprocessing failed: {e}")
        return Response(
            {'error': 'Failed to reprocess credential'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
