# Generated by Django 4.2.7 on 2025-06-16 20:44

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('credentials', '0001_initial'),
        ('candidates', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='FacilityProfile',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('facility_name', models.CharField(help_text='Official name of the healthcare facility', max_length=200)),
                ('facility_type', models.CharField(choices=[('hospital', 'Hospital'), ('clinic', 'Clinic'), ('urgent_care', 'Urgent Care'), ('long_term_care', 'Long-term Care'), ('assisted_living', 'Assisted Living'), ('home_health', 'Home Health'), ('rehabilitation', 'Rehabilitation Center'), ('mental_health', 'Mental Health Facility'), ('surgery_center', 'Surgery Center'), ('dialysis', 'Dialysis Center'), ('imaging', 'Imaging Center'), ('laboratory', 'Laboratory'), ('pharmacy', 'Pharmacy'), ('other', 'Other')], help_text='Type of healthcare facility', max_length=20)),
                ('size', models.CharField(choices=[('small', 'Small (1-50 employees)'), ('medium', 'Medium (51-200 employees)'), ('large', 'Large (201-500 employees)'), ('enterprise', 'Enterprise (500+ employees)')], help_text='Size of the facility by employee count', max_length=20)),
                ('address_line_1', models.CharField(max_length=255)),
                ('address_line_2', models.CharField(blank=True, max_length=255)),
                ('city', models.CharField(max_length=100)),
                ('state', models.CharField(max_length=50)),
                ('zip_code', models.CharField(max_length=20)),
                ('country', models.CharField(default='United States', max_length=100)),
                ('main_phone', models.CharField(blank=True, max_length=20)),
                ('hr_phone', models.CharField(blank=True, max_length=20)),
                ('hr_email', models.EmailField(blank=True, max_length=254)),
                ('website', models.URLField(blank=True)),
                ('bed_count', models.PositiveIntegerField(blank=True, help_text='Number of beds (for hospitals/care facilities)', null=True)),
                ('accreditations', models.JSONField(default=list, help_text='Accreditations and certifications (JSON array)')),
                ('culture_description', models.TextField(blank=True, help_text='Description of facility culture and work environment', max_length=2000)),
                ('mission_statement', models.TextField(blank=True, help_text='Facility mission statement', max_length=1000)),
                ('values', models.JSONField(default=list, help_text='Core values (JSON array)')),
                ('benefits_offered', models.JSONField(default=list, help_text='Benefits and perks offered (JSON array)')),
                ('culture_audit_completed', models.BooleanField(default=False, help_text='Whether facility has completed culture audit')),
                ('culture_score', models.FloatField(default=0.0, help_text='Culture assessment score (0-100)', validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(100.0)])),
                ('leadership_style', models.CharField(blank=True, help_text='Primary leadership style', max_length=50)),
                ('team_dynamics', models.CharField(blank=True, help_text='Team dynamics preference', max_length=50)),
                ('shift_patterns', models.JSONField(default=list, help_text='Available shift patterns (JSON array)')),
                ('staffing_model', models.CharField(blank=True, help_text='Staffing model (e.g., Primary Nursing, Team Nursing)', max_length=100)),
                ('employee_satisfaction_score', models.FloatField(blank=True, help_text='Employee satisfaction score', null=True, validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(100.0)])),
                ('patient_satisfaction_score', models.FloatField(blank=True, help_text='Patient satisfaction score', null=True, validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(100.0)])),
                ('turnover_rate', models.FloatField(blank=True, help_text='Annual turnover rate percentage', null=True, validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(100.0)])),
                ('is_verified', models.BooleanField(default=False, help_text='Facility verification status')),
                ('verification_date', models.DateTimeField(blank=True, help_text='Date facility was verified', null=True)),
                ('profile_completeness', models.FloatField(default=0.0, help_text='Profile completeness percentage', validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(100.0)])),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('specialties', models.ManyToManyField(blank=True, help_text='Medical specialties offered', to='candidates.specialty')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='facility_profile', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Facility Profile',
                'verbose_name_plural': 'Facility Profiles',
                'db_table': 'facilities_profile',
            },
        ),
        migrations.CreateModel(
            name='JobPosting',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(help_text='Job title', max_length=200)),
                ('department', models.CharField(blank=True, help_text='Department or unit', max_length=100)),
                ('employment_type', models.CharField(choices=[('full_time', 'Full Time'), ('part_time', 'Part Time'), ('contract', 'Contract'), ('travel', 'Travel Assignment'), ('prn', 'PRN/Per Diem'), ('temp', 'Temporary')], help_text='Type of employment', max_length=20)),
                ('shift_type', models.CharField(blank=True, help_text='Shift type (Day, Evening, Night, Rotating)', max_length=20)),
                ('schedule', models.CharField(blank=True, help_text='Work schedule details', max_length=100)),
                ('minimum_experience_years', models.PositiveIntegerField(default=0, help_text='Minimum years of experience required')),
                ('description', models.TextField(help_text='Detailed job description')),
                ('responsibilities', models.TextField(blank=True, help_text='Key responsibilities and duties')),
                ('qualifications', models.TextField(blank=True, help_text='Required qualifications and skills')),
                ('salary_min', models.DecimalField(blank=True, decimal_places=2, help_text='Minimum salary/annual compensation', max_digits=10, null=True)),
                ('salary_max', models.DecimalField(blank=True, decimal_places=2, help_text='Maximum salary/annual compensation', max_digits=10, null=True)),
                ('hourly_rate_min', models.DecimalField(blank=True, decimal_places=2, help_text='Minimum hourly rate', max_digits=6, null=True)),
                ('hourly_rate_max', models.DecimalField(blank=True, decimal_places=2, help_text='Maximum hourly rate', max_digits=6, null=True)),
                ('benefits', models.JSONField(default=list, help_text='Benefits offered for this position (JSON array)')),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('active', 'Active'), ('paused', 'Paused'), ('filled', 'Filled'), ('cancelled', 'Cancelled'), ('expired', 'Expired')], default='draft', max_length=20)),
                ('priority', models.CharField(choices=[('low', 'Low'), ('normal', 'Normal'), ('high', 'High'), ('urgent', 'Urgent')], default='normal', max_length=20)),
                ('positions_available', models.PositiveIntegerField(default=1, help_text='Number of positions to fill')),
                ('posted_date', models.DateTimeField(blank=True, help_text='Date job was posted publicly', null=True)),
                ('application_deadline', models.DateField(blank=True, help_text='Application deadline', null=True)),
                ('start_date', models.DateField(blank=True, help_text='Desired start date', null=True)),
                ('is_rapid_response', models.BooleanField(default=False, help_text='Available for RapidResponse emergency staffing')),
                ('rapid_response_premium', models.DecimalField(blank=True, decimal_places=2, help_text='Premium rate multiplier for rapid response (e.g., 1.5)', max_digits=5, null=True)),
                ('culture_fit_weight', models.FloatField(default=0.3, help_text='Weight of culture fit in matching algorithm (0.0-1.0)', validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(1.0)])),
                ('skills_weight', models.FloatField(default=0.4, help_text='Weight of skills match in matching algorithm (0.0-1.0)', validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(1.0)])),
                ('experience_weight', models.FloatField(default=0.3, help_text='Weight of experience in matching algorithm (0.0-1.0)', validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(1.0)])),
                ('view_count', models.PositiveIntegerField(default=0, help_text='Number of times job posting has been viewed')),
                ('application_count', models.PositiveIntegerField(default=0, help_text='Number of applications received')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('facility', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='job_postings', to='facilities.facilityprofile')),
                ('preferred_credentials', models.ManyToManyField(blank=True, help_text='Preferred credentials and certifications', related_name='preferred_for_jobs', to='credentials.credentialtype')),
                ('required_credentials', models.ManyToManyField(blank=True, help_text='Required credentials and certifications', to='credentials.credentialtype')),
                ('specialties', models.ManyToManyField(blank=True, help_text='Required or preferred specialties', to='candidates.specialty')),
            ],
            options={
                'verbose_name': 'Job Posting',
                'verbose_name_plural': 'Job Postings',
                'db_table': 'facilities_job_posting',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['facility', 'status'], name='facilities__facilit_6aae83_idx'), models.Index(fields=['status', 'priority'], name='facilities__status_2d6cd0_idx'), models.Index(fields=['employment_type'], name='facilities__employm_3868f2_idx'), models.Index(fields=['is_rapid_response'], name='facilities__is_rapi_1da48b_idx'), models.Index(fields=['posted_date'], name='facilities__posted__44560c_idx')],
            },
        ),
        migrations.AddIndex(
            model_name='facilityprofile',
            index=models.Index(fields=['facility_type'], name='facilities__facilit_2295e3_idx'),
        ),
        migrations.AddIndex(
            model_name='facilityprofile',
            index=models.Index(fields=['size'], name='facilities__size_ccd4ce_idx'),
        ),
        migrations.AddIndex(
            model_name='facilityprofile',
            index=models.Index(fields=['city', 'state'], name='facilities__city_9eed54_idx'),
        ),
        migrations.AddIndex(
            model_name='facilityprofile',
            index=models.Index(fields=['is_verified'], name='facilities__is_veri_b4ef2c_idx'),
        ),
    ]
