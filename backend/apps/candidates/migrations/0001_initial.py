# Generated by Django 4.2.7 on 2025-06-16 20:44

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Specialty',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Name of the specialty (e.g., ICU, Emergency, Pediatrics)', max_length=100, unique=True)),
                ('category', models.CharField(choices=[('nursing', 'Nursing'), ('therapy', 'Therapy'), ('technical', 'Technical'), ('administrative', 'Administrative'), ('support', 'Support Services'), ('clinical', 'Clinical')], help_text='Category of specialty', max_length=20)),
                ('description', models.TextField(blank=True, help_text='Description of the specialty')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this specialty is currently active')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Specialty',
                'verbose_name_plural': 'Specialties',
                'db_table': 'candidates_specialty',
                'ordering': ['category', 'name'],
            },
        ),
        migrations.CreateModel(
            name='CandidateProfile',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('professional_title', models.CharField(help_text='Current professional title (e.g., Registered Nurse, Medical Assistant)', max_length=100)),
                ('experience_level', models.CharField(choices=[('new_grad', 'New Graduate'), ('entry_level', 'Entry Level (0-2 years)'), ('experienced', 'Experienced (3-5 years)'), ('senior', 'Senior (6-10 years)'), ('expert', 'Expert (10+ years)')], default='new_grad', max_length=20)),
                ('years_experience', models.PositiveIntegerField(default=0, help_text='Total years of healthcare experience', validators=[django.core.validators.MaxValueValidator(50)])),
                ('preferred_employment_types', models.JSONField(default=list, help_text='Preferred employment types (JSON array)')),
                ('preferred_shift', models.CharField(choices=[('day', 'Day Shift'), ('evening', 'Evening Shift'), ('night', 'Night Shift'), ('rotating', 'Rotating Shifts'), ('flexible', 'Flexible')], default='flexible', max_length=20)),
                ('willing_to_travel', models.BooleanField(default=False, help_text='Open to travel assignments')),
                ('max_travel_distance', models.PositiveIntegerField(blank=True, help_text='Maximum travel distance in miles', null=True)),
                ('desired_hourly_rate_min', models.DecimalField(blank=True, decimal_places=2, help_text='Minimum desired hourly rate', max_digits=6, null=True)),
                ('desired_hourly_rate_max', models.DecimalField(blank=True, decimal_places=2, help_text='Maximum desired hourly rate', max_digits=6, null=True)),
                ('desired_salary_min', models.DecimalField(blank=True, decimal_places=2, help_text='Minimum desired annual salary', max_digits=10, null=True)),
                ('desired_salary_max', models.DecimalField(blank=True, decimal_places=2, help_text='Maximum desired annual salary', max_digits=10, null=True)),
                ('available_start_date', models.DateField(blank=True, help_text='Earliest available start date', null=True)),
                ('is_actively_seeking', models.BooleanField(default=True, help_text='Currently seeking new opportunities')),
                ('professional_summary', models.TextField(blank=True, help_text='Professional summary or objective', max_length=2000)),
                ('technical_skills', models.JSONField(default=list, help_text='Technical skills and competencies (JSON array)')),
                ('soft_skills', models.JSONField(default=list, help_text='Soft skills and interpersonal abilities (JSON array)')),
                ('references_available', models.BooleanField(default=False, help_text='Professional references available upon request')),
                ('carebot_engagement_score', models.FloatField(default=0.0, help_text='CareBot engagement score (0-100)', validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(100.0)])),
                ('last_carebot_interaction', models.DateTimeField(blank=True, help_text='Last interaction with CareBot system', null=True)),
                ('profile_completeness', models.FloatField(default=0.0, help_text='Profile completeness percentage', validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(100.0)])),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('specialties', models.ManyToManyField(blank=True, help_text='Areas of specialization', to='candidates.specialty')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='candidate_profile', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Candidate Profile',
                'verbose_name_plural': 'Candidate Profiles',
                'db_table': 'candidates_profile',
            },
        ),
        migrations.CreateModel(
            name='WorkExperience',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('job_title', models.CharField(help_text='Job title or position', max_length=100)),
                ('employer_name', models.CharField(help_text='Name of employer/facility', max_length=200)),
                ('employer_type', models.CharField(blank=True, help_text='Type of facility (e.g., Hospital, Clinic, Long-term Care)', max_length=100)),
                ('city', models.CharField(blank=True, max_length=100)),
                ('state', models.CharField(blank=True, max_length=50)),
                ('start_date', models.DateField(help_text='Start date of employment')),
                ('end_date', models.DateField(blank=True, help_text='End date of employment (leave blank if current)', null=True)),
                ('is_current', models.BooleanField(default=False, help_text='Currently employed in this position')),
                ('description', models.TextField(blank=True, help_text='Job responsibilities and achievements')),
                ('is_verified', models.BooleanField(default=False, help_text='Employment verified by employer')),
                ('verification_contact', models.CharField(blank=True, help_text='Contact person for verification', max_length=200)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('candidate', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='work_experiences', to='candidates.candidateprofile')),
                ('specialties', models.ManyToManyField(blank=True, help_text='Specialties practiced in this role', to='candidates.specialty')),
            ],
            options={
                'verbose_name': 'Work Experience',
                'verbose_name_plural': 'Work Experiences',
                'db_table': 'candidates_work_experience',
                'ordering': ['-start_date'],
                'indexes': [models.Index(fields=['candidate', '-start_date'], name='candidates__candida_4623dd_idx'), models.Index(fields=['is_current'], name='candidates__is_curr_11f7ce_idx')],
            },
        ),
        migrations.CreateModel(
            name='Education',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('institution_name', models.CharField(help_text='Name of educational institution', max_length=200)),
                ('degree_type', models.CharField(choices=[('certificate', 'Certificate'), ('diploma', 'Diploma'), ('associate', 'Associate Degree'), ('bachelor', 'Bachelor Degree'), ('master', 'Master Degree'), ('doctorate', 'Doctorate'), ('other', 'Other')], help_text='Type of degree or credential', max_length=20)),
                ('field_of_study', models.CharField(help_text='Field of study or major', max_length=100)),
                ('start_date', models.DateField(help_text='Start date of program')),
                ('graduation_date', models.DateField(blank=True, help_text='Graduation date (leave blank if in progress)', null=True)),
                ('is_in_progress', models.BooleanField(default=False, help_text='Currently enrolled in this program')),
                ('gpa', models.DecimalField(blank=True, decimal_places=2, help_text='Grade Point Average (0.0-4.0 scale)', max_digits=3, null=True, validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(4.0)])),
                ('honors', models.CharField(blank=True, help_text='Academic honors or distinctions', max_length=100)),
                ('is_verified', models.BooleanField(default=False, help_text='Education verified by institution')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('candidate', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='education', to='candidates.candidateprofile')),
            ],
            options={
                'verbose_name': 'Education',
                'verbose_name_plural': 'Education',
                'db_table': 'candidates_education',
                'ordering': ['-graduation_date', '-start_date'],
                'indexes': [models.Index(fields=['candidate', '-graduation_date'], name='candidates__candida_d97178_idx'), models.Index(fields=['degree_type'], name='candidates__degree__25f013_idx')],
            },
        ),
        migrations.AddIndex(
            model_name='candidateprofile',
            index=models.Index(fields=['experience_level'], name='candidates__experie_44fc0d_idx'),
        ),
        migrations.AddIndex(
            model_name='candidateprofile',
            index=models.Index(fields=['is_actively_seeking'], name='candidates__is_acti_e18fd0_idx'),
        ),
        migrations.AddIndex(
            model_name='candidateprofile',
            index=models.Index(fields=['available_start_date'], name='candidates__availab_16f1e9_idx'),
        ),
        migrations.AddIndex(
            model_name='candidateprofile',
            index=models.Index(fields=['carebot_engagement_score'], name='candidates__carebot_575199_idx'),
        ),
    ]
