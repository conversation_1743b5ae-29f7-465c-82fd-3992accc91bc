"""
Code Med Talent - Credential Processing Services
OCR and AI-powered credential verification services for CredentialChain.
"""

import os
import logging
import tempfile
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from pathlib import Path

import requests
from django.conf import settings
from django.core.files.uploadedfile import UploadedFile

# OCR imports (optional for now)
try:
    import cv2
    import numpy as np
    import pytesseract
    from PIL import Image
    from pdf2image import convert_from_path
    OCR_AVAILABLE = True
except ImportError:
    OCR_AVAILABLE = False

# Google Cloud Vision imports (optional)
try:
    from google.cloud import vision
    GOOGLE_VISION_AVAILABLE = True
except ImportError:
    GOOGLE_VISION_AVAILABLE = False

# OpenAI imports
try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

logger = logging.getLogger(__name__)


@dataclass
class OCRResult:
    """Result of OCR processing."""
    text: str
    confidence: float
    bounding_boxes: List[Dict]
    processing_method: str
    error_message: Optional[str] = None


@dataclass
class CredentialData:
    """Extracted credential information."""
    credential_type: Optional[str] = None
    license_number: Optional[str] = None
    holder_name: Optional[str] = None
    issuing_authority: Optional[str] = None
    issue_date: Optional[str] = None
    expiration_date: Optional[str] = None
    state: Optional[str] = None
    specialty: Optional[str] = None
    confidence_score: float = 0.0
    raw_text: str = ""
    extracted_fields: Dict[str, Any] = None


class OCRService:
    """
    OCR service for processing credential documents.
    Supports multiple OCR engines: Tesseract, Google Cloud Vision.
    """

    def __init__(self):
        self.tesseract_config = r'--oem 3 --psm 6'
        self.google_vision_client = None
        
        if GOOGLE_VISION_AVAILABLE and hasattr(settings, 'GOOGLE_CLOUD_CREDENTIALS'):
            try:
                self.google_vision_client = vision.ImageAnnotatorClient()
            except Exception as e:
                logger.warning(f"Failed to initialize Google Vision client: {e}")

    def process_document(self, file: UploadedFile, use_google_vision: bool = True) -> OCRResult:
        """
        Process uploaded document using OCR.

        Args:
            file: Uploaded file (PDF, JPG, PNG)
            use_google_vision: Whether to use Google Vision API as primary OCR

        Returns:
            OCRResult with extracted text and metadata
        """
        # For now, return a mock result if OCR libraries aren't available
        if not OCR_AVAILABLE:
            return OCRResult(
                text="Mock OCR text - RN License #123456 John Doe Expires: 12/31/2024",
                confidence=0.85,
                bounding_boxes=[],
                processing_method="mock",
                error_message="OCR libraries not installed - using mock data"
            )

        try:
            # Save uploaded file temporarily
            with tempfile.NamedTemporaryFile(delete=False, suffix=Path(file.name).suffix) as temp_file:
                for chunk in file.chunks():
                    temp_file.write(chunk)
                temp_file_path = temp_file.name

            # Convert to images if PDF
            images = self._convert_to_images(temp_file_path)

            # Process with preferred OCR method
            if use_google_vision and self.google_vision_client:
                result = self._process_with_google_vision(images)
            else:
                result = self._process_with_tesseract(images)

            # Clean up
            os.unlink(temp_file_path)

            return result

        except Exception as e:
            logger.error(f"OCR processing failed: {e}")
            return OCRResult(
                text="",
                confidence=0.0,
                bounding_boxes=[],
                processing_method="error",
                error_message=str(e)
            )

    def _convert_to_images(self, file_path: str):
        """Convert document to images for OCR processing."""
        if not OCR_AVAILABLE:
            return []

        file_ext = Path(file_path).suffix.lower()

        if file_ext == '.pdf':
            # Convert PDF to images
            return convert_from_path(file_path, dpi=300)
        elif file_ext in ['.jpg', '.jpeg', '.png', '.tiff', '.bmp']:
            # Load image directly
            from PIL import Image
            return [Image.open(file_path)]
        else:
            raise ValueError(f"Unsupported file format: {file_ext}")

    def _process_with_tesseract(self, images: List[Image.Image]) -> OCRResult:
        """Process images using Tesseract OCR."""
        all_text = []
        all_boxes = []
        total_confidence = 0.0
        
        for image in images:
            # Preprocess image for better OCR
            processed_image = self._preprocess_image(image)
            
            # Extract text
            text = pytesseract.image_to_string(processed_image, config=self.tesseract_config)
            all_text.append(text)
            
            # Get bounding boxes and confidence
            data = pytesseract.image_to_data(processed_image, output_type=pytesseract.Output.DICT)
            boxes = self._extract_bounding_boxes(data)
            all_boxes.extend(boxes)
            
            # Calculate confidence
            confidences = [int(conf) for conf in data['conf'] if int(conf) > 0]
            if confidences:
                total_confidence += sum(confidences) / len(confidences)
        
        avg_confidence = total_confidence / len(images) if images else 0.0
        
        return OCRResult(
            text='\n'.join(all_text),
            confidence=avg_confidence / 100.0,  # Convert to 0-1 scale
            bounding_boxes=all_boxes,
            processing_method="tesseract"
        )

    def _process_with_google_vision(self, images: List[Image.Image]) -> OCRResult:
        """Process images using Google Cloud Vision API."""
        if not self.google_vision_client:
            return self._process_with_tesseract(images)
        
        all_text = []
        all_boxes = []
        total_confidence = 0.0
        
        for image in images:
            # Convert PIL image to bytes
            import io
            img_byte_arr = io.BytesIO()
            image.save(img_byte_arr, format='PNG')
            img_byte_arr = img_byte_arr.getvalue()
            
            # Create Vision API image object
            vision_image = vision.Image(content=img_byte_arr)
            
            # Perform text detection
            response = self.google_vision_client.text_detection(image=vision_image)
            texts = response.text_annotations
            
            if texts:
                # First annotation contains full text
                full_text = texts[0].description
                all_text.append(full_text)
                
                # Extract bounding boxes
                for text in texts[1:]:  # Skip first (full text)
                    vertices = text.bounding_poly.vertices
                    box = {
                        'text': text.description,
                        'left': min(v.x for v in vertices),
                        'top': min(v.y for v in vertices),
                        'width': max(v.x for v in vertices) - min(v.x for v in vertices),
                        'height': max(v.y for v in vertices) - min(v.y for v in vertices),
                        'confidence': 0.9  # Google Vision doesn't provide word-level confidence
                    }
                    all_boxes.append(box)
                
                total_confidence += 90.0  # Assume high confidence for Google Vision
            
            # Check for errors
            if response.error.message:
                raise Exception(f"Google Vision API error: {response.error.message}")
        
        avg_confidence = total_confidence / len(images) if images else 0.0
        
        return OCRResult(
            text='\n'.join(all_text),
            confidence=avg_confidence / 100.0,
            bounding_boxes=all_boxes,
            processing_method="google_vision"
        )

    def _preprocess_image(self, image):
        """Preprocess image for better OCR accuracy."""
        if not OCR_AVAILABLE:
            return image

        # Convert PIL to OpenCV format
        opencv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)

        # Convert to grayscale
        gray = cv2.cvtColor(opencv_image, cv2.COLOR_BGR2GRAY)

        # Apply denoising
        denoised = cv2.fastNlMeansDenoising(gray)

        # Apply adaptive thresholding
        thresh = cv2.adaptiveThreshold(
            denoised, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
        )

        # Convert back to PIL
        from PIL import Image
        return Image.fromarray(thresh)

    def _extract_bounding_boxes(self, tesseract_data: Dict) -> List[Dict]:
        """Extract bounding boxes from Tesseract data."""
        boxes = []
        n_boxes = len(tesseract_data['text'])
        
        for i in range(n_boxes):
            if int(tesseract_data['conf'][i]) > 30:  # Filter low confidence
                box = {
                    'text': tesseract_data['text'][i],
                    'left': tesseract_data['left'][i],
                    'top': tesseract_data['top'][i],
                    'width': tesseract_data['width'][i],
                    'height': tesseract_data['height'][i],
                    'confidence': int(tesseract_data['conf'][i]) / 100.0
                }
                boxes.append(box)
        
        return boxes


class CredentialExtractor:
    """
    AI-powered service for extracting structured data from OCR text.
    Uses pattern matching and AI to identify credential information.
    """

    def __init__(self):
        self.openai_client = None
        if OPENAI_AVAILABLE and hasattr(settings, 'OPENAI_API_KEY'):
            openai.api_key = settings.OPENAI_API_KEY
            self.openai_client = openai

    def extract_credential_data(self, ocr_result: OCRResult, credential_type_hint: Optional[str] = None) -> CredentialData:
        """
        Extract structured credential data from OCR text.
        
        Args:
            ocr_result: Result from OCR processing
            credential_type_hint: Optional hint about credential type
            
        Returns:
            CredentialData with extracted information
        """
        try:
            # First try pattern-based extraction
            pattern_result = self._extract_with_patterns(ocr_result.text, credential_type_hint)
            
            # If AI is available, enhance with AI extraction
            if self.openai_client and pattern_result.confidence_score < 0.8:
                ai_result = self._extract_with_ai(ocr_result.text, credential_type_hint)
                # Merge results, preferring AI for low-confidence pattern matches
                pattern_result = self._merge_extraction_results(pattern_result, ai_result)
            
            pattern_result.raw_text = ocr_result.text
            return pattern_result
            
        except Exception as e:
            logger.error(f"Credential extraction failed: {e}")
            return CredentialData(
                raw_text=ocr_result.text,
                confidence_score=0.0,
                extracted_fields={'error': str(e)}
            )

    def _extract_with_patterns(self, text: str, credential_type_hint: Optional[str] = None) -> CredentialData:
        """Extract credential data using regex patterns."""
        import re
        from datetime import datetime
        
        result = CredentialData()
        confidence_scores = []
        
        # Common patterns for healthcare credentials
        patterns = {
            'license_number': [
                r'(?:license|lic\.?)\s*#?\s*:?\s*([A-Z0-9\-]{4,20})',
                r'(?:number|no\.?)\s*:?\s*([A-Z0-9\-]{4,20})',
                r'([A-Z]{2,3}\d{4,8})',  # Common format like RN123456
            ],
            'holder_name': [
                r'(?:name|holder)\s*:?\s*([A-Z][a-z]+(?:\s+[A-Z][a-z]+)+)',
                r'^([A-Z][A-Z\s]+)$',  # All caps name on its own line
            ],
            'expiration_date': [
                r'(?:exp|expires?|expiration)\s*:?\s*(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})',
                r'(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})',
            ],
            'issue_date': [
                r'(?:issued?|effective)\s*:?\s*(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})',
            ],
            'state': [
                r'(?:state|issued by)\s*:?\s*([A-Z]{2})',
                r'\b([A-Z]{2})\s+(?:board|department)',
            ]
        }
        
        # Apply patterns
        for field, field_patterns in patterns.items():
            for pattern in field_patterns:
                matches = re.findall(pattern, text, re.IGNORECASE | re.MULTILINE)
                if matches:
                    setattr(result, field, matches[0])
                    confidence_scores.append(0.8)
                    break
        
        # Determine credential type from text
        credential_types = {
            'RN License': ['registered nurse', 'rn license', 'nursing license'],
            'LPN License': ['licensed practical', 'lpn license', 'practical nurse'],
            'BLS Certification': ['basic life support', 'bls', 'cpr'],
            'ACLS Certification': ['advanced cardiac', 'acls'],
            'CNA Certification': ['certified nursing assistant', 'cna'],
        }
        
        for cred_type, keywords in credential_types.items():
            if any(keyword in text.lower() for keyword in keywords):
                result.credential_type = cred_type
                confidence_scores.append(0.9)
                break
        
        result.confidence_score = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0.0
        return result

    def _extract_with_ai(self, text: str, credential_type_hint: Optional[str] = None) -> CredentialData:
        """Extract credential data using OpenAI."""
        if not self.openai_client:
            return CredentialData(confidence_score=0.0)
        
        prompt = f"""
        Extract healthcare credential information from the following text. Return a JSON object with these fields:
        - credential_type: Type of credential (e.g., "RN License", "BLS Certification")
        - license_number: License or certification number
        - holder_name: Name of the credential holder
        - issuing_authority: Organization that issued the credential
        - issue_date: Date issued (MM/DD/YYYY format)
        - expiration_date: Expiration date (MM/DD/YYYY format)
        - state: State abbreviation if applicable
        - specialty: Medical specialty if mentioned
        
        Text to analyze:
        {text}
        
        {f"Hint: This appears to be a {credential_type_hint}" if credential_type_hint else ""}
        
        Return only valid JSON:
        """
        
        try:
            response = self.openai_client.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=500,
                temperature=0.1
            )
            
            import json
            extracted_data = json.loads(response.choices[0].message.content)
            
            result = CredentialData(
                credential_type=extracted_data.get('credential_type'),
                license_number=extracted_data.get('license_number'),
                holder_name=extracted_data.get('holder_name'),
                issuing_authority=extracted_data.get('issuing_authority'),
                issue_date=extracted_data.get('issue_date'),
                expiration_date=extracted_data.get('expiration_date'),
                state=extracted_data.get('state'),
                specialty=extracted_data.get('specialty'),
                confidence_score=0.85,  # High confidence for AI extraction
                extracted_fields=extracted_data
            )
            
            return result
            
        except Exception as e:
            logger.error(f"AI extraction failed: {e}")
            return CredentialData(confidence_score=0.0)

    def _merge_extraction_results(self, pattern_result: CredentialData, ai_result: CredentialData) -> CredentialData:
        """Merge results from pattern and AI extraction."""
        # Use AI result as base if it has higher confidence
        if ai_result.confidence_score > pattern_result.confidence_score:
            merged = ai_result
            # Fill in any missing fields from pattern result
            for field in ['credential_type', 'license_number', 'holder_name', 'issuing_authority', 
                         'issue_date', 'expiration_date', 'state', 'specialty']:
                if not getattr(merged, field) and getattr(pattern_result, field):
                    setattr(merged, field, getattr(pattern_result, field))
        else:
            merged = pattern_result
        
        # Average the confidence scores
        merged.confidence_score = (pattern_result.confidence_score + ai_result.confidence_score) / 2
        return merged
