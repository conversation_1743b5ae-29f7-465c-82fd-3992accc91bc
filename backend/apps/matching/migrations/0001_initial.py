# Generated by Django 4.2.7 on 2025-06-16 20:44

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('candidates', '0001_initial'),
        ('facilities', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Match',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('overall_score', models.FloatField(help_text='Overall match score (0-100)', validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(100.0)])),
                ('culture_fit_score', models.FloatField(help_text='Culture fit score (0-100)', validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(100.0)])),
                ('skills_match_score', models.FloatField(help_text='Skills match score (0-100)', validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(100.0)])),
                ('experience_match_score', models.FloatField(help_text='Experience match score (0-100)', validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(100.0)])),
                ('credentials_match_score', models.FloatField(help_text='Credentials match score (0-100)', validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(100.0)])),
                ('location_match_score', models.FloatField(help_text='Location/travel preference match score (0-100)', validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(100.0)])),
                ('compensation_match_score', models.FloatField(help_text='Compensation expectation match score (0-100)', validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(100.0)])),
                ('match_reasons', models.JSONField(default=list, help_text='Reasons for the match (JSON array)')),
                ('potential_concerns', models.JSONField(default=list, help_text='Potential concerns or gaps (JSON array)')),
                ('status', models.CharField(choices=[('pending', 'Pending Review'), ('viewed', 'Viewed by Candidate'), ('interested', 'Candidate Interested'), ('applied', 'Application Submitted'), ('rejected', 'Candidate Rejected'), ('expired', 'Match Expired')], default='pending', max_length=20)),
                ('candidate_viewed_at', models.DateTimeField(blank=True, help_text='When candidate first viewed the match', null=True)),
                ('candidate_response_at', models.DateTimeField(blank=True, help_text='When candidate responded to the match', null=True)),
                ('model_version', models.CharField(default='v1.0', help_text='Version of matching algorithm used', max_length=50)),
                ('confidence_level', models.FloatField(help_text='AI confidence in the match (0.0-1.0)', validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(1.0)])),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('expires_at', models.DateTimeField(blank=True, help_text='When the match expires', null=True)),
                ('candidate', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='matches', to='candidates.candidateprofile')),
                ('job_posting', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='matches', to='facilities.jobposting')),
            ],
            options={
                'verbose_name': 'Match',
                'verbose_name_plural': 'Matches',
                'db_table': 'matching_match',
                'ordering': ['-overall_score', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Application',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('status', models.CharField(choices=[('submitted', 'Submitted'), ('under_review', 'Under Review'), ('interview_scheduled', 'Interview Scheduled'), ('interviewed', 'Interviewed'), ('reference_check', 'Reference Check'), ('offer_extended', 'Offer Extended'), ('offer_accepted', 'Offer Accepted'), ('offer_declined', 'Offer Declined'), ('rejected', 'Rejected'), ('withdrawn', 'Withdrawn')], default='submitted', max_length=20)),
                ('cover_letter', models.TextField(blank=True, help_text='Candidate cover letter')),
                ('additional_notes', models.TextField(blank=True, help_text='Additional notes from candidate')),
                ('requested_salary', models.DecimalField(blank=True, decimal_places=2, help_text='Candidate requested salary', max_digits=10, null=True)),
                ('requested_hourly_rate', models.DecimalField(blank=True, decimal_places=2, help_text='Candidate requested hourly rate', max_digits=6, null=True)),
                ('submitted_at', models.DateTimeField(auto_now_add=True)),
                ('reviewed_at', models.DateTimeField(blank=True, help_text='When application was first reviewed', null=True)),
                ('interview_scheduled_at', models.DateTimeField(blank=True, help_text='When interview was scheduled', null=True)),
                ('decision_made_at', models.DateTimeField(blank=True, help_text='When final decision was made', null=True)),
                ('facility_notes', models.TextField(blank=True, help_text='Internal notes from facility')),
                ('rejection_reason', models.CharField(blank=True, help_text='Reason for rejection (if applicable)', max_length=200)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('candidate', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='applications', to='candidates.candidateprofile')),
                ('job_posting', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='applications', to='facilities.jobposting')),
                ('match', models.OneToOneField(blank=True, help_text='Associated match that led to this application', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='application', to='matching.match')),
            ],
            options={
                'verbose_name': 'Application',
                'verbose_name_plural': 'Applications',
                'db_table': 'matching_application',
                'ordering': ['-submitted_at'],
            },
        ),
        migrations.AddIndex(
            model_name='match',
            index=models.Index(fields=['candidate', 'status'], name='matching_ma_candida_378c1e_idx'),
        ),
        migrations.AddIndex(
            model_name='match',
            index=models.Index(fields=['job_posting', 'status'], name='matching_ma_job_pos_85980b_idx'),
        ),
        migrations.AddIndex(
            model_name='match',
            index=models.Index(fields=['overall_score'], name='matching_ma_overall_44752a_idx'),
        ),
        migrations.AddIndex(
            model_name='match',
            index=models.Index(fields=['created_at'], name='matching_ma_created_9824e1_idx'),
        ),
        migrations.AddIndex(
            model_name='match',
            index=models.Index(fields=['expires_at'], name='matching_ma_expires_fc79f4_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='match',
            unique_together={('candidate', 'job_posting')},
        ),
        migrations.AddIndex(
            model_name='application',
            index=models.Index(fields=['candidate', 'status'], name='matching_ap_candida_025203_idx'),
        ),
        migrations.AddIndex(
            model_name='application',
            index=models.Index(fields=['job_posting', 'status'], name='matching_ap_job_pos_d6250c_idx'),
        ),
        migrations.AddIndex(
            model_name='application',
            index=models.Index(fields=['status'], name='matching_ap_status_ad0cce_idx'),
        ),
        migrations.AddIndex(
            model_name='application',
            index=models.Index(fields=['submitted_at'], name='matching_ap_submitt_edd545_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='application',
            unique_together={('candidate', 'job_posting')},
        ),
    ]
