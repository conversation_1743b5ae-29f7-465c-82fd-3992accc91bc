"""
Code Med Talent - Credentials Serializers
API serializers for CredentialChain system.
"""

from rest_framework import serializers
from django.core.files.uploadedfile import UploadedFile
from django.core.validators import FileExtensionValidator

from .models import CredentialType, Credential


class CredentialTypeSerializer(serializers.ModelSerializer):
    """
    Serializer for CredentialType model.
    """
    
    class Meta:
        model = CredentialType
        fields = [
            'id', 'name', 'category', 'description', 'issuing_authority',
            'verification_url', 'typical_validity_period', 'required_for_roles',
            'verification_requirements', 'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class CredentialSerializer(serializers.ModelSerializer):
    """
    Serializer for Credential model.
    """
    credential_type_name = serializers.CharField(source='credential_type.name', read_only=True)
    credential_type_category = serializers.Char<PERSON>ield(source='credential_type.category', read_only=True)
    verified_by_name = serializers.CharField(source='verified_by.get_full_name', read_only=True)
    is_expired = serializers.SerializerMethodField()
    days_until_expiration = serializers.SerializerMethodField()
    verification_status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = Credential
        fields = [
            'id', 'user', 'credential_type', 'credential_type_name', 'credential_type_category',
            'license_number', 'holder_name', 'issuing_authority', 'issue_date', 'expiration_date',
            'status', 'verification_status_display', 'verification_date', 'verification_source',
            'verification_notes', 'verified_by', 'verified_by_name', 'blockchain_hash',
            'document_file', 'ai_extracted_data', 'is_expired', 'days_until_expiration',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'user', 'verification_date', 'verified_by', 'verified_by_name',
            'blockchain_hash', 'ai_extracted_data', 'is_expired', 'days_until_expiration',
            'created_at', 'updated_at', 'credential_type_name', 'credential_type_category',
            'verification_status_display'
        ]
    
    def get_is_expired(self, obj):
        """Check if credential is expired."""
        return obj.is_expired()
    
    def get_days_until_expiration(self, obj):
        """Get days until expiration."""
        return obj.days_until_expiration()


class CredentialUploadSerializer(serializers.Serializer):
    """
    Serializer for credential document upload and processing.
    """
    document_file = serializers.FileField(
        validators=[FileExtensionValidator(['pdf', 'jpg', 'jpeg', 'png', 'tiff'])],
        help_text="Upload credential document (PDF, JPG, PNG, TIFF)"
    )
    credential_type_id = serializers.UUIDField(
        required=False,
        help_text="Optional: Specify credential type ID if known"
    )
    use_google_vision = serializers.BooleanField(
        default=True,
        help_text="Use Google Cloud Vision API for OCR (fallback to Tesseract if false)"
    )
    
    def validate_document_file(self, value: UploadedFile):
        """Validate uploaded document file."""
        # Check file size (max 10MB)
        max_size = 10 * 1024 * 1024  # 10MB
        if value.size > max_size:
            raise serializers.ValidationError(
                f"File size too large. Maximum size is {max_size / (1024*1024):.1f}MB"
            )
        
        # Check file type
        allowed_types = [
            'application/pdf',
            'image/jpeg',
            'image/jpg', 
            'image/png',
            'image/tiff'
        ]
        
        if value.content_type not in allowed_types:
            raise serializers.ValidationError(
                f"Unsupported file type: {value.content_type}. "
                f"Allowed types: {', '.join(allowed_types)}"
            )
        
        return value
    
    def validate_credential_type_id(self, value):
        """Validate credential type exists."""
        if value:
            try:
                CredentialType.objects.get(id=value)
            except CredentialType.DoesNotExist:
                raise serializers.ValidationError("Invalid credential type ID")
        return value


class CredentialVerificationSerializer(serializers.Serializer):
    """
    Serializer for manual credential verification.
    """
    status = serializers.ChoiceField(
        choices=[
            (Credential.Status.VERIFIED, 'Verified'),
            (Credential.Status.INVALID, 'Invalid/Rejected'),
            (Credential.Status.EXPIRED, 'Expired'),
            (Credential.Status.REVOKED, 'Revoked'),
        ],
        help_text="New verification status"
    )
    verification_notes = serializers.CharField(
        max_length=1000,
        required=False,
        allow_blank=True,
        help_text="Notes about the verification process"
    )
    verification_source = serializers.CharField(
        max_length=200,
        required=False,
        allow_blank=True,
        help_text="Source used for verification (e.g., 'State Board Website', 'Phone Verification')"
    )


class CredentialSummarySerializer(serializers.ModelSerializer):
    """
    Lightweight serializer for credential summaries.
    """
    credential_type_name = serializers.CharField(source='credential_type.name', read_only=True)
    is_expired = serializers.SerializerMethodField()
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = Credential
        fields = [
            'id', 'credential_type_name', 'license_number', 'status', 'status_display',
            'expiration_date', 'is_expired', 'verification_date'
        ]
    
    def get_is_expired(self, obj):
        """Check if credential is expired."""
        return obj.is_expired()


class CredentialPublicSerializer(serializers.ModelSerializer):
    """
    Public serializer for verified credentials (for agencies/facilities).
    Only shows verified credentials with limited information.
    """
    credential_type_name = serializers.CharField(source='credential_type.name', read_only=True)
    credential_type_category = serializers.CharField(source='credential_type.category', read_only=True)
    holder_name = serializers.SerializerMethodField()
    is_expired = serializers.SerializerMethodField()
    verification_status = serializers.SerializerMethodField()
    
    class Meta:
        model = Credential
        fields = [
            'id', 'credential_type_name', 'credential_type_category',
            'holder_name', 'issuing_authority', 'expiration_date',
            'is_expired', 'verification_status', 'verification_date',
            'blockchain_hash'
        ]
    
    def get_holder_name(self, obj):
        """Return holder name only for verified credentials."""
        if obj.status == Credential.Status.VERIFIED:
            return obj.holder_name
        return "Not Verified"
    
    def get_is_expired(self, obj):
        """Check if credential is expired."""
        return obj.is_expired()
    
    def get_verification_status(self, obj):
        """Return verification status."""
        return {
            'status': obj.status,
            'status_display': obj.get_status_display(),
            'verified': obj.status == Credential.Status.VERIFIED,
            'verification_date': obj.verification_date
        }


class CredentialBulkUploadSerializer(serializers.Serializer):
    """
    Serializer for bulk credential upload.
    """
    credentials = serializers.ListField(
        child=CredentialUploadSerializer(),
        max_length=10,
        help_text="List of credentials to upload (max 10)"
    )
    
    def validate_credentials(self, value):
        """Validate bulk upload list."""
        if len(value) == 0:
            raise serializers.ValidationError("At least one credential must be provided")
        
        if len(value) > 10:
            raise serializers.ValidationError("Maximum 10 credentials can be uploaded at once")
        
        return value


class CredentialSearchSerializer(serializers.Serializer):
    """
    Serializer for credential search parameters.
    """
    credential_type = serializers.CharField(required=False, help_text="Filter by credential type name")
    category = serializers.ChoiceField(
        choices=CredentialType.Category.choices,
        required=False,
        help_text="Filter by credential category"
    )
    status = serializers.ChoiceField(
        choices=Credential.Status.choices,
        required=False,
        help_text="Filter by verification status"
    )
    expiring_within_days = serializers.IntegerField(
        required=False,
        min_value=1,
        max_value=365,
        help_text="Find credentials expiring within specified days"
    )
    verified_only = serializers.BooleanField(
        default=False,
        help_text="Show only verified credentials"
    )
    include_expired = serializers.BooleanField(
        default=False,
        help_text="Include expired credentials in results"
    )


class CredentialStatsSerializer(serializers.Serializer):
    """
    Serializer for credential statistics.
    """
    total_credentials = serializers.IntegerField()
    verified_credentials = serializers.IntegerField()
    pending_credentials = serializers.IntegerField()
    expired_credentials = serializers.IntegerField()
    by_category = serializers.DictField()
    expiring_soon = serializers.IntegerField(required=False)
    completion_rate = serializers.FloatField(required=False)


class CredentialExportSerializer(serializers.Serializer):
    """
    Serializer for credential export options.
    """
    format = serializers.ChoiceField(
        choices=[('csv', 'CSV'), ('pdf', 'PDF'), ('json', 'JSON')],
        default='csv',
        help_text="Export format"
    )
    include_documents = serializers.BooleanField(
        default=False,
        help_text="Include document files in export (PDF/ZIP only)"
    )
    verified_only = serializers.BooleanField(
        default=True,
        help_text="Export only verified credentials"
    )
    credential_types = serializers.ListField(
        child=serializers.UUIDField(),
        required=False,
        help_text="Filter by specific credential type IDs"
    )
