# Generated by Django 4.2.7 on 2025-06-16 20:44

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='CommunicationChannel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Name of the communication channel', max_length=100, unique=True)),
                ('channel_type', models.CharField(choices=[('email', 'Email'), ('sms', 'SMS'), ('phone', 'Phone Call'), ('linkedin', 'LinkedIn Message'), ('push', 'Push Notification'), ('in_app', 'In-App Message')], help_text='Type of communication channel', max_length=20)),
                ('is_active', models.BooleanField(default=True, help_text='Whether this channel is currently active')),
                ('api_endpoint', models.URLField(blank=True, help_text='API endpoint for this channel')),
                ('api_key', models.CharField(blank=True, help_text='API key or credentials', max_length=200)),
                ('rate_limit_per_hour', models.PositiveIntegerField(default=100, help_text='Maximum messages per hour')),
                ('rate_limit_per_day', models.PositiveIntegerField(default=1000, help_text='Maximum messages per day')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Communication Channel',
                'verbose_name_plural': 'Communication Channels',
                'db_table': 'communications_channel',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='MessageTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Template name', max_length=200, unique=True)),
                ('category', models.CharField(choices=[('welcome', 'Welcome Message'), ('nurturing', 'Relationship Nurturing'), ('job_alert', 'Job Alert'), ('follow_up', 'Follow Up'), ('ceu_opportunity', 'CEU Opportunity'), ('career_advice', 'Career Advice'), ('industry_news', 'Industry News'), ('feedback_request', 'Feedback Request'), ('reengagement', 'Re-engagement')], help_text='Message category', max_length=20)),
                ('subject', models.CharField(blank=True, help_text='Message subject (for email/push notifications)', max_length=200)),
                ('content', models.TextField(help_text='Message content with placeholder variables')),
                ('personalization_level', models.CharField(choices=[('low', 'Low Personalization'), ('medium', 'Medium Personalization'), ('high', 'High Personalization'), ('dynamic', 'Dynamic AI Personalization')], default='medium', max_length=20)),
                ('variables', models.JSONField(default=list, help_text='Available template variables (JSON array)')),
                ('use_ai_enhancement', models.BooleanField(default=False, help_text='Use AI to enhance message content')),
                ('ai_tone', models.CharField(blank=True, help_text='AI tone preference (e.g., professional, friendly, empathetic)', max_length=50)),
                ('usage_count', models.PositiveIntegerField(default=0, help_text='Number of times template has been used')),
                ('success_rate', models.FloatField(default=0.0, help_text='Success rate percentage', validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(100.0)])),
                ('is_active', models.BooleanField(default=True, help_text='Whether template is active')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('channel', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='templates', to='communications.communicationchannel')),
            ],
            options={
                'verbose_name': 'Message Template',
                'verbose_name_plural': 'Message Templates',
                'db_table': 'communications_message_template',
                'ordering': ['category', 'name'],
            },
        ),
        migrations.CreateModel(
            name='CommunicationLog',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('subject', models.CharField(blank=True, help_text='Message subject', max_length=200)),
                ('content', models.TextField(help_text='Final message content sent')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('sent', 'Sent'), ('delivered', 'Delivered'), ('opened', 'Opened'), ('clicked', 'Clicked'), ('replied', 'Replied'), ('failed', 'Failed'), ('bounced', 'Bounced'), ('unsubscribed', 'Unsubscribed')], default='pending', max_length=20)),
                ('recipient_address', models.CharField(help_text='Email address, phone number, etc.', max_length=200)),
                ('sent_at', models.DateTimeField(blank=True, help_text='When message was sent', null=True)),
                ('delivered_at', models.DateTimeField(blank=True, help_text='When message was delivered', null=True)),
                ('opened_at', models.DateTimeField(blank=True, help_text='When message was first opened', null=True)),
                ('clicked_at', models.DateTimeField(blank=True, help_text='When links in message were clicked', null=True)),
                ('replied_at', models.DateTimeField(blank=True, help_text='When recipient replied', null=True)),
                ('error_message', models.TextField(blank=True, help_text='Error message if delivery failed')),
                ('retry_count', models.PositiveIntegerField(default=0, help_text='Number of retry attempts')),
                ('is_carebot_generated', models.BooleanField(default=False, help_text='Whether message was generated by CareBot AI')),
                ('carebot_context', models.JSONField(default=dict, help_text='CareBot context and reasoning (JSON object)')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('channel', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='communications', to='communications.communicationchannel')),
                ('recipient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='communications_received', to=settings.AUTH_USER_MODEL)),
                ('sender', models.ForeignKey(blank=True, help_text='User who initiated the communication (if applicable)', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='communications_sent', to=settings.AUTH_USER_MODEL)),
                ('template', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='communications', to='communications.messagetemplate')),
            ],
            options={
                'verbose_name': 'Communication Log',
                'verbose_name_plural': 'Communication Logs',
                'db_table': 'communications_log',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddIndex(
            model_name='messagetemplate',
            index=models.Index(fields=['category'], name='communicati_categor_6d06e7_idx'),
        ),
        migrations.AddIndex(
            model_name='messagetemplate',
            index=models.Index(fields=['channel'], name='communicati_channel_a04771_idx'),
        ),
        migrations.AddIndex(
            model_name='messagetemplate',
            index=models.Index(fields=['is_active'], name='communicati_is_acti_e49b6b_idx'),
        ),
        migrations.AddIndex(
            model_name='communicationlog',
            index=models.Index(fields=['recipient', 'status'], name='communicati_recipie_ca4f6c_idx'),
        ),
        migrations.AddIndex(
            model_name='communicationlog',
            index=models.Index(fields=['channel', 'status'], name='communicati_channel_8da27b_idx'),
        ),
        migrations.AddIndex(
            model_name='communicationlog',
            index=models.Index(fields=['status'], name='communicati_status_c6cc67_idx'),
        ),
        migrations.AddIndex(
            model_name='communicationlog',
            index=models.Index(fields=['sent_at'], name='communicati_sent_at_1e46c3_idx'),
        ),
        migrations.AddIndex(
            model_name='communicationlog',
            index=models.Index(fields=['is_carebot_generated'], name='communicati_is_care_1dc35c_idx'),
        ),
    ]
